<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" class="trancy-zh-CN light">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>北汽声量合伙人管理系统 - 任务管理</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #303133;
        }
        
        .app-wrapper {
            display: flex;
            height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar-container {
            width: 210px;
            background: #304156;
            transition: width 0.28s;
            flex-shrink: 0;
        }
        
        .sidebar-logo-container {
            position: relative;
            width: 100%;
            height: 50px;
            line-height: 50px;
            background: #2b2f3a;
            text-align: center;
            overflow: hidden;
        }
        
        .sidebar-logo-link {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-decoration: none;
        }
        
        .sidebar-logo {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            background: #409eff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .sidebar-title {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin: 0;
        }
        
        .el-scrollbar {
            height: calc(100vh - 50px);
            overflow-y: auto;
        }
        
        .el-menu {
            border: none;
            height: 100%;
            width: 100% !important;
            background-color: #304156;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .el-menu-item, .el-sub-menu__title {
            color: #bfcbd9;
            border-bottom: none;
            height: 50px;
            line-height: 50px;
            padding: 0 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
        }
        
        .el-menu-item:hover, .el-sub-menu__title:hover {
            background-color: #263445;
            color: #fff;
        }
        
        .el-menu-item.is-active {
            background-color: #409eff;
            color: #fff;
        }
        
        .el-sub-menu.is-active > .el-sub-menu__title {
            color: #fff;
            background-color: #263445;
        }
        
        .el-sub-menu.is-opened > .el-sub-menu__title {
            background-color: #263445;
        }
        
        .el-menu--inline {
            background-color: #1f2d3d;
        }
        
        .el-menu--inline .el-menu-item {
            padding-left: 50px;
            background-color: #1f2d3d;
        }
        
        .el-menu--inline .el-menu-item:hover {
            background-color: #001528;
        }
        
        .el-menu--inline .el-menu-item.is-active {
            background-color: #409eff;
        }
        
        .menu-title {
            margin-left: 10px;
        }
        
        .el-sub-menu__icon-arrow {
            margin-left: auto;
            transition: transform 0.3s;
        }
        
        .el-sub-menu.is-opened .el-sub-menu__icon-arrow {
            transform: rotate(90deg);
        }
        
        /* 主容器样式 */
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 导航栏样式 */
        .navbar {
            height: 50px;
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: relative;
            z-index: 10;
        }
        
        .hamburger-container {
            line-height: 46px;
            height: 100%;
            cursor: pointer;
            transition: background .3s;
            margin-right: 20px;
        }
        
        .breadcrumb-container {
            margin-left: 20px;
        }
        
        .el-breadcrumb__item {
            font-size: 14px;
            line-height: 1;
            display: inline-block;
        }
        
        .el-breadcrumb__inner {
            color: #606266;
            cursor: pointer;
            text-decoration: none;
        }
        
        .el-breadcrumb__inner:hover {
            color: #409eff;
        }
        
        .el-breadcrumb__separator {
            margin: 0 9px;
            font-weight: 500;
            color: #c0c4cc;
        }
        
        .right-menu {
            margin-left: auto;
            display: flex;
            align-items: center;
        }
        
        .right-menu-item {
            display: inline-block;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .right-menu-item:hover {
            background: rgba(0, 0, 0, .025);
        }
        
        .avatar-container {
            margin-right: 30px;
        }
        
        .avatar-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            margin-right: 8px;
        }
        
        /* 标签页样式 */
        .tags-view-container {
            height: 34px;
            width: 100%;
            background: #fff;
            border-bottom: 1px solid #d8dce5;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
            overflow: hidden;
        }
        
        .tags-view-wrapper {
            height: 34px;
            overflow: hidden;
            padding: 0 20px;
            display: flex;
            align-items: center;
        }
        
        .tags-view-item {
            display: inline-block;
            position: relative;
            cursor: pointer;
            height: 26px;
            line-height: 26px;
            border: 1px solid #d8dce5;
            color: #495060;
            background: #fff;
            padding: 0 8px;
            font-size: 12px;
            margin-right: 6px;
            text-decoration: none;
            border-radius: 2px;
        }
        
        .tags-view-item.active {
            background-color: #42b983;
            color: #fff;
            border-color: #42b983;
        }
        
        /* 主内容区域 */
        .app-main {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .p-2 {
            padding: 20px;
        }
        
        .mb10 {
            margin-bottom: 10px;
        }

        /* 表单样式 */
        .el-form--inline .el-form-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 20px;
            vertical-align: top;
        }

        .el-form-item__label {
            font-weight: 500;
            color: #606266;
            text-align: right;
            vertical-align: middle;
            float: left;
            font-size: 14px;
            padding: 0 12px 0 0;
            line-height: 32px;
            box-sizing: border-box;
        }

        .el-form-item__content {
            line-height: 32px;
            position: relative;
            font-size: 14px;
        }

        /* 卡片样式 */
        .el-card {
            border-radius: 4px;
            border: 1px solid #ebeef5;
            background-color: #fff;
            overflow: hidden;
            color: #303133;
            transition: .3s;
            margin-bottom: 20px;
        }

        .el-card.is-hover-shadow:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .el-card.is-always-shadow {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .el-card__header {
            padding: 18px 20px;
            border-bottom: 1px solid #ebeef5;
            box-sizing: border-box;
        }

        .el-card__body {
            padding: 20px;
        }

        /* 行列布局 */
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }

        .el-col {
            flex: 0 0 auto;
        }

        .el-col-1\.5 {
            width: 12.5%;
        }

        /* 按钮样式 */
        .el-button {
            display: inline-block;
            line-height: 1;
            white-space: nowrap;
            cursor: pointer;
            background: #fff;
            border: 1px solid #dcdfe6;
            color: #606266;
            text-align: center;
            box-sizing: border-box;
            outline: none;
            margin: 0;
            transition: .1s;
            font-weight: 500;
            padding: 12px 20px;
            font-size: 14px;
            border-radius: 4px;
            text-decoration: none;
            margin-right: 10px;
        }

        .el-button:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }

        .el-button--primary {
            color: #fff;
            background-color: #409eff;
            border-color: #409eff;
        }

        .el-button--primary:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #fff;
        }

        .el-button--warning {
            color: #fff;
            background-color: #e6a23c;
            border-color: #e6a23c;
        }

        .el-button--warning:hover {
            background: #ebb563;
            border-color: #ebb563;
            color: #fff;
        }

        .el-button.is-plain {
            background: #fff;
            border: 1px solid #dcdfe6;
            color: #606266;
        }

        .el-button--primary.is-plain {
            color: #409eff;
            background: #ecf5ff;
            border-color: #b3d8ff;
        }

        .el-button--primary.is-plain:hover {
            background: #409eff;
            border-color: #409eff;
            color: #fff;
        }

        .el-button--warning.is-plain {
            color: #e6a23c;
            background: #fdf6ec;
            border-color: #f5dab1;
        }

        .el-button--warning.is-plain:hover {
            background: #e6a23c;
            border-color: #e6a23c;
            color: #fff;
        }

        .el-button.is-circle {
            border-radius: 50%;
            padding: 12px;
        }

        .el-button .fas {
            margin-right: 5px;
        }

        .el-button .fas:last-child {
            margin-right: 0;
        }

        /* 输入框样式 */
        .el-input {
            position: relative;
            font-size: 14px;
            display: inline-block;
            width: 100%;
        }

        .el-input__wrapper {
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: #fff;
            background-image: none;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            box-sizing: border-box;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            height: 32px;
        }

        .el-input__wrapper:hover {
            border-color: #c0c4cc;
        }

        .el-input__wrapper.is-focus {
            border-color: #409eff;
        }

        .el-input__inner {
            width: 100%;
            flex-grow: 1;
            -webkit-appearance: none;
            color: #606266;
            font-size: inherit;
            height: 30px;
            line-height: 30px;
            padding: 0;
            outline: none;
            border: none;
            background: none;
            box-sizing: border-box;
        }

        .el-input__inner::placeholder {
            color: #a8abb2;
        }

        /* 选择器样式 */
        .el-select {
            display: inline-block;
            position: relative;
            width: 100%;
        }

        .el-select__wrapper {
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: #fff;
            background-image: none;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            box-sizing: border-box;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            height: 32px;
        }

        .el-select__wrapper:hover {
            border-color: #c0c4cc;
        }

        .el-select__selection {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            flex: 1;
            min-width: 0;
        }

        .el-select__selected-item {
            line-height: inherit;
            height: inherit;
            user-select: none;
        }

        .el-select__placeholder {
            color: #a8abb2;
        }

        .el-select__suffix {
            display: inline-flex;
            align-items: center;
            color: #c0c4cc;
            font-size: 14px;
        }

        /* 日期选择器样式 */
        .el-date-editor {
            position: relative;
            display: inline-block;
            text-align: left;
        }

        .el-range-editor {
            display: inline-flex;
            align-items: center;
            padding: 3px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            background-color: #fff;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            width: 100%;
        }

        .el-range-editor:hover {
            border-color: #c0c4cc;
        }

        .el-range-input {
            appearance: none;
            border: none;
            outline: none;
            display: inline-block;
            height: 100%;
            margin: 0;
            padding: 0;
            width: 39%;
            text-align: center;
            font-size: 14px;
            color: #606266;
            background-color: transparent;
        }

        .el-range-input::placeholder {
            color: #c0c4cc;
        }

        .el-range-separator {
            flex: 1;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            padding: 0 5px;
            margin: 0;
            font-size: 14px;
            word-break: keep-all;
            color: #303133;
        }

        .el-range__icon {
            font-size: 14px;
            color: #c0c4cc;
            width: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .el-range__close-icon {
            font-size: 14px;
            color: #c0c4cc;
            width: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        /* 表格样式 */
        .el-table {
            width: 100%;
            max-width: 100%;
            background-color: #fff;
            border: 1px solid #ebeef5;
            font-size: 14px;
            color: #606266;
            border-collapse: separate;
            border-spacing: 0;
        }

        .el-table__cell {
            padding: 12px 0;
            min-width: 0;
            box-sizing: border-box;
            text-overflow: ellipsis;
            vertical-align: middle;
            position: relative;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
        }

        .el-table__cell:last-child {
            border-right: none;
        }

        .el-table__cell .cell {
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
            word-break: break-all;
            line-height: 23px;
            padding: 0 12px;
        }

        .el-table thead .el-table__cell {
            background-color: #fafafa;
            color: #909399;
            font-weight: 500;
        }

        .el-table__row {
            background-color: #fff;
        }

        .el-table__row:hover {
            background-color: #f5f7fa;
        }

        .el-table-fixed-column--right {
            position: sticky;
            right: 0;
            background-color: inherit;
            z-index: 2;
        }

        /* 标签样式 */
        .el-tag {
            background-color: #ecf5ff;
            border-color: #d9ecff;
            color: #409eff;
            display: inline-block;
            height: 32px;
            padding: 0 10px;
            line-height: 30px;
            font-size: 12px;
            border-width: 1px;
            border-style: solid;
            border-radius: 4px;
            box-sizing: border-box;
            white-space: nowrap;
        }

        .el-tag--success {
            background-color: #f0f9ff;
            border-color: #c2e7b0;
            color: #67c23a;
        }

        .el-tag--warning {
            background-color: #fdf6ec;
            border-color: #f5dab1;
            color: #e6a23c;
        }

        .el-tag--danger {
            background-color: #fef0f0;
            border-color: #fbc4c4;
            color: #f56c6c;
        }

        .el-button--danger {
            color: #fff;
            background-color: #f56c6c;
            border-color: #f56c6c;
        }

        .el-button--danger:hover {
            background: #f78989;
            border-color: #f78989;
            color: #fff;
        }

        .el-button--danger.is-plain {
            color: #f56c6c;
            background: #fef0f0;
            border-color: #fbc4c4;
        }

        .el-button--danger.is-plain:hover {
            background: #f56c6c;
            border-color: #f56c6c;
            color: #fff;
        }

        /* 工具栏样式 */
        .top-right-btn {
            margin-left: auto;
        }

        /* 弹窗样式 */
        .el-overlay {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 2000;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .el-overlay-dialog {
            position: relative;
            margin: 0 auto 50px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            box-sizing: border-box;
            width: 50%;
        }

        .el-dialog {
            position: relative;
            margin: 15vh auto 50px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            box-sizing: border-box;
            width: 50%;
        }

        .el-dialog__header {
            padding: 20px 20px 10px;
            position: relative;
        }

        .el-dialog__title {
            line-height: 24px;
            font-size: 18px;
            color: #303133;
        }

        .el-dialog__headerbtn {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 0;
            background: transparent;
            border: none;
            outline: none;
            cursor: pointer;
            font-size: 16px;
        }

        .el-dialog__close {
            color: #909399;
        }

        .el-dialog__body {
            padding: 30px 20px;
            color: #606266;
            font-size: 14px;
            word-break: break-all;
        }

        .el-dialog__footer {
            padding: 10px 20px 20px;
            text-align: right;
            box-sizing: border-box;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 10px;
        }

        .el-textarea {
            position: relative;
            display: inline-block;
            width: 100%;
            vertical-align: bottom;
            font-size: 14px;
        }

        .el-textarea__inner {
            display: block;
            resize: vertical;
            padding: 5px 11px;
            line-height: 1.5;
            box-sizing: border-box;
            width: 100%;
            font-size: inherit;
            color: #606266;
            background-color: #fff;
            background-image: none;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .el-date-editor {
            position: relative;
            display: inline-block;
            text-align: left;
        }

        .el-input__suffix {
            position: absolute;
            height: 100%;
            right: 5px;
            top: 0;
            text-align: center;
            color: #c0c4cc;
            transition: all 0.3s;
            pointer-events: none;
        }

        .el-input__suffix-inner {
            pointer-events: all;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 54px;
            }

            .sidebar-title {
                display: none;
            }

            .el-form--inline .el-form-item {
                display: block;
                margin-right: 0;
            }

            .el-dialog {
                width: 90%;
                margin: 5vh auto 50px;
            }

            .table-container {
                font-size: 12px;
            }
        }
    </style>
</head>

<body>
    <div id="app" data-v-app="">
        <div class="app-wrapper">
            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <div class="sidebar-logo-container">
                    <a href="/manage/" class="sidebar-logo-link">
                        <div class="sidebar-logo">北</div>
                        <h1 class="sidebar-title">北汽声量合伙人</h1>
                    </a>
                </div>
                <div class="el-scrollbar">
                    <ul class="el-menu el-menu--vertical">
                        <li>
                            <a href="/manage/index">
                                <div class="el-menu-item">
                                    <i class="fas fa-home"></i>
                                    <span class="menu-title">首页</span>
                                </div>
                            </a>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-users"></i>
                                <span class="menu-title">用户管理</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/userManagement/staff">
                                        <div class="el-menu-item">
                                            <i class="fas fa-user-tie"></i>
                                            <span class="menu-title">员工管理</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/userManagement/user">
                                        <div class="el-menu-item">
                                            <i class="fas fa-user"></i>
                                            <span class="menu-title">用户管理</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-photo-video"></i>
                                <span class="menu-title">媒体管理</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/mediaManagement/media">
                                        <div class="el-menu-item">
                                            <i class="fas fa-photo-video"></i>
                                            <span class="menu-title">媒体管理</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu is-active is-opened">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-tasks"></i>
                                <span class="menu-title">媒体任务</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline">
                                <li>
                                    <a href="/manage/mediaTask/mediaTask" class="router-link-active router-link-exact-active">
                                        <div class="el-menu-item is-active">
                                            <i class="fas fa-list-alt"></i>
                                            <span class="menu-title">任务管理</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/mediaTask/taskAudit">
                                        <div class="el-menu-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span class="menu-title">任务审核</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-car"></i>
                                <span class="menu-title">预约试驾</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/reservationTestDrive/customerRetention">
                                        <div class="el-menu-item">
                                            <i class="fas fa-clipboard-list"></i>
                                            <span class="menu-title">留资记录</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/reservationTestDrive/template">
                                        <div class="el-menu-item">
                                            <i class="fas fa-image"></i>
                                            <span class="menu-title">海报模板管理</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/reservationTestDrive/vehicleManagement">
                                        <div class="el-menu-item">
                                            <i class="fas fa-car-side"></i>
                                            <span class="menu-title">车型管理</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-edit"></i>
                                <span class="menu-title">内容发布</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/publicationManagement/operation/article">
                                        <div class="el-menu-item">
                                            <i class="fas fa-newspaper"></i>
                                            <span class="menu-title">文章管理</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/publicationManagement/announcementManagement">
                                        <div class="el-menu-item">
                                            <i class="fas fa-bullhorn"></i>
                                            <span class="menu-title">公告管理</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-cogs"></i>
                                <span class="menu-title">平台运营</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/operation/advertisement">
                                        <div class="el-menu-item">
                                            <i class="fas fa-ad"></i>
                                            <span class="menu-title">广告管理</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/operation/myPageConfig">
                                        <div class="el-menu-item">
                                            <i class="fas fa-sliders-h"></i>
                                            <span class="menu-title">我的页面配置</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/operation/Information">
                                        <div class="el-menu-item">
                                            <i class="fas fa-envelope"></i>
                                            <span class="menu-title">信息触达管理</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="el-sub-menu">
                            <div class="el-sub-menu__title">
                                <i class="fas fa-chart-bar"></i>
                                <span class="menu-title">数据统计</span>
                                <i class="fas fa-chevron-right el-sub-menu__icon-arrow"></i>
                            </div>
                            <ul class="el-menu el-menu--inline" style="display: none;">
                                <li>
                                    <a href="/manage/data/taskEngagementData">
                                        <div class="el-menu-item">
                                            <i class="fas fa-chart-line"></i>
                                            <span class="menu-title">任务参与数据</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/data/originalQuestData">
                                        <div class="el-menu-item">
                                            <i class="fas fa-chart-pie"></i>
                                            <span class="menu-title">原创任务数据</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/data/calloutConversions">
                                        <div class="el-menu-item">
                                            <i class="fas fa-exchange-alt"></i>
                                            <span class="menu-title">邀约转化数据</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/data/shareCommentLike">
                                        <div class="el-menu-item">
                                            <i class="fas fa-thumbs-up"></i>
                                            <span class="menu-title">转评赞任务参与表</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/data/departmentData">
                                        <div class="el-menu-item">
                                            <i class="fas fa-building"></i>
                                            <span class="menu-title">部门统计表</span>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a href="/manage/data/organizationList">
                                        <div class="el-menu-item">
                                            <i class="fas fa-trophy"></i>
                                            <span class="menu-title">组织参与排名</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 主容器 -->
            <div class="main-container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <div class="hamburger-container">
                        <i class="fas fa-bars"></i>
                    </div>
                    <div class="breadcrumb-container">
                        <span class="el-breadcrumb__item">
                            <span class="el-breadcrumb__inner">
                                <a href="#">首页</a>
                            </span>
                            <span class="el-breadcrumb__separator">/</span>
                        </span>
                        <span class="el-breadcrumb__item">
                            <span class="el-breadcrumb__inner">媒体任务</span>
                            <span class="el-breadcrumb__separator">/</span>
                        </span>
                        <span class="el-breadcrumb__item">
                            <span class="el-breadcrumb__inner">任务管理</span>
                            <span class="el-breadcrumb__separator">/</span>
                        </span>
                    </div>
                    <div class="right-menu">
                        <div class="right-menu-item">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="right-menu-item">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="right-menu-item">
                            <i class="fas fa-expand"></i>
                        </div>
                        <div class="right-menu-item">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="right-menu-item">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="avatar-container">
                            <div class="avatar-wrapper">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMTAiIGZpbGw9IiM0MDllZmYiLz4KPHRleHQgeD0iMjAiIHk9IjI2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QTwvdGV4dD4KPHN2Zz4=" class="user-avatar" alt="用户头像" />
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页 -->
                <div class="tags-view-container">
                    <div class="tags-view-wrapper">
                        <a href="/manage/index" class="tags-view-item">首页</a>
                        <a href="/manage/mediaTask/mediaTask" class="tags-view-item active">任务管理 <i class="fas fa-times"></i></a>
                        <a href="/manage/publicationManagement/operation/article" class="tags-view-item">文章管理 <i class="fas fa-times"></i></a>
                    </div>
                </div>

                <!-- 主内容区域 -->
                <section class="app-main">
                    <div class="p-2">
                        <!-- 搜索表单 -->
                        <div class="mb10">
                            <div class="el-card is-hover-shadow">
                                <div class="el-card__body">
                                    <form class="el-form el-form--default el-form--label-right el-form--inline">
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务ID</label>
                                            <div class="el-form-item__content">
                                                <div class="el-input">
                                                    <div class="el-input__wrapper">
                                                        <input class="el-input__inner" type="text" placeholder="请输入任务ID" maxlength="19" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务名称</label>
                                            <div class="el-form-item__content">
                                                <div class="el-input">
                                                    <div class="el-input__wrapper">
                                                        <input class="el-input__inner" type="text" placeholder="请输入任务名称" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务类型</label>
                                            <div class="el-form-item__content">
                                                <div class="el-select">
                                                    <div class="el-select__wrapper">
                                                        <div class="el-select__selection">
                                                            <div class="el-select__selected-item el-select__placeholder">
                                                                <span>请选择任务类型</span>
                                                            </div>
                                                        </div>
                                                        <div class="el-select__suffix">
                                                            <i class="fas fa-chevron-down"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务状态</label>
                                            <div class="el-form-item__content">
                                                <div class="el-select">
                                                    <div class="el-select__wrapper">
                                                        <div class="el-select__selection">
                                                            <div class="el-select__selected-item el-select__placeholder">
                                                                <span>请选择任务状态</span>
                                                            </div>
                                                        </div>
                                                        <div class="el-select__suffix">
                                                            <i class="fas fa-chevron-down"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">发布时间</label>
                                            <div class="el-form-item__content">
                                                <div class="el-date-editor el-range-editor">
                                                    <i class="fas fa-calendar-alt el-range__icon"></i>
                                                    <input class="el-range-input" placeholder="开始时间" />
                                                    <span class="el-range-separator">—</span>
                                                    <input class="el-range-input" placeholder="结束时间" />
                                                    <i class="fas fa-times el-range__close-icon" style="display: none;"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">截止时间</label>
                                            <div class="el-form-item__content">
                                                <div class="el-date-editor el-range-editor">
                                                    <i class="fas fa-calendar-alt el-range__icon"></i>
                                                    <input class="el-range-input" placeholder="开始时间" />
                                                    <span class="el-range-separator">—</span>
                                                    <input class="el-range-input" placeholder="结束时间" />
                                                    <i class="fas fa-times el-range__close-icon" style="display: none;"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">发布账号</label>
                                            <div class="el-form-item__content">
                                                <div class="el-input">
                                                    <div class="el-input__wrapper">
                                                        <input class="el-input__inner" type="text" placeholder="请输入发布账号" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <div class="el-form-item__content">
                                                <button type="button" class="el-button el-button--primary">
                                                    <i class="fas fa-search"></i>
                                                    <span>查询</span>
                                                </button>
                                                <button type="button" class="el-button">
                                                    <i class="fas fa-redo"></i>
                                                    <span>重置</span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="el-card is-always-shadow">
                            <div class="el-card__header">
                                <div class="el-row">
                                    <div class="el-col el-col-1.5">
                                        <button type="button" class="el-button el-button--primary is-plain" onclick="showNewTaskDialog()">
                                            <i class="fas fa-plus"></i>
                                            <span>新建任务</span>
                                        </button>
                                    </div>
                                    <div class="el-col el-col-1.5">
                                        <button type="button" class="el-button el-button--warning is-plain">
                                            <i class="fas fa-download"></i>
                                            <span>导出</span>
                                        </button>
                                    </div>
                                    <div class="top-right-btn">
                                        <div class="el-row">
                                            <button type="button" class="el-button is-circle">
                                                <i class="fas fa-search"></i>
                                            </button>
                                            <button type="button" class="el-button is-circle">
                                                <i class="fas fa-refresh"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-card__body">
                                <div class="table-container" style="overflow-x: auto;">
                                    <table class="el-table" style="min-width: 2500px;">
                                        <thead>
                                            <tr>
                                                <th class="el-table__cell is-center" style="width: 200px;">
                                                    <div class="cell">ID</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 200px;">
                                                    <div class="cell">任务名称</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 200px;">
                                                    <div class="cell">任务类型</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 80px;">
                                                    <div class="cell">发布账号</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 300px;">
                                                    <div class="cell">任务链接</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 150px;">
                                                    <div class="cell">任务预算(元)</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 150px;">
                                                    <div class="cell">实消预算(元)</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 100px;">
                                                    <div class="cell">领取任务数</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 100px;">
                                                    <div class="cell">提交任务数</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 100px;">
                                                    <div class="cell">完成任务数</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 100px;">
                                                    <div class="cell">PV</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 100px;">
                                                    <div class="cell">UV</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 80px;">
                                                    <div class="cell">有效触达</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 250px;">
                                                    <div class="cell">任务发布时间</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 150px;">
                                                    <div class="cell">任务截止时间</div>
                                                </th>
                                                <th class="el-table__cell is-center" style="width: 150px;">
                                                    <div class="cell">任务状态</div>
                                                </th>
                                                <th class="el-table__cell is-center el-table-fixed-column--right" style="width: 300px; position: sticky; right: 0; background: #fafafa; z-index: 2;">
                                                    <div class="cell">操作</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="el-table__row">
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">1001</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">北汽新能源推广任务</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">
                                                        <span class="el-tag el-tag--success">推广任务</span>
                                                    </div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">admin</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">
                                                        <a href="https://example.com/task/1001" target="_blank" style="color: #409eff;">https://example.com/task/1001</a>
                                                    </div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">10000</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">8500</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">150</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">120</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">100</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">25000</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">18000</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">15000</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">2025-07-25 10:00:00</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">2025-08-25 23:59:59</div>
                                                </td>
                                                <td class="el-table__cell is-center">
                                                    <div class="cell">
                                                        <span class="el-tag el-tag--success">已上架</span>
                                                    </div>
                                                </td>
                                                <td class="el-table__cell is-center el-table-fixed-column--right" style="position: sticky; right: 0; background: #fff; z-index: 2;">
                                                    <div class="cell">
                                                        <button type="button" class="el-button el-button--primary is-plain" style="margin-right: 5px;">
                                                            <i class="fas fa-edit"></i>
                                                            <span>编辑</span>
                                                        </button>
                                                        <button type="button" class="el-button el-button--warning is-plain" style="margin-right: 5px;">
                                                            <i class="fas fa-eye"></i>
                                                            <span>查看</span>
                                                        </button>
                                                        <button type="button" class="el-button el-button--danger is-plain">
                                                            <i class="fas fa-trash"></i>
                                                            <span>删除</span>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 新建任务弹窗 -->
    <div id="newTaskDialog" class="el-overlay" style="display: none; z-index: 2000;">
        <div class="el-overlay-dialog">
            <div class="el-dialog" style="margin-top: 15vh; width: 50%;">
                <div class="el-dialog__header">
                    <span class="el-dialog__title">新建任务</span>
                    <button class="el-dialog__headerbtn" onclick="hideNewTaskDialog()">
                        <i class="fas fa-times el-dialog__close"></i>
                    </button>
                </div>
                <div class="el-dialog__body">
                    <form class="el-form el-form--default el-form--label-right">
                        <!-- 基础信息 -->
                        <div class="form-section">
                            <h3>基础信息</h3>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务名称</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="text" placeholder="请输入任务名称" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务类型</label>
                                <div class="el-form-item__content">
                                    <div class="el-select">
                                        <div class="el-select__wrapper">
                                            <div class="el-select__selection">
                                                <div class="el-select__selected-item el-select__placeholder">
                                                    <span>请选择任务类型</span>
                                                </div>
                                            </div>
                                            <div class="el-select__suffix">
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务描述</label>
                                <div class="el-form-item__content">
                                    <div class="el-textarea">
                                        <textarea class="el-textarea__inner" placeholder="请输入任务描述" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 任务设置 -->
                        <div class="form-section">
                            <h3>任务设置</h3>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务预算</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="number" placeholder="请输入任务预算" />
                                        </div>
                                        <div class="el-input__suffix">
                                            <span class="el-input__suffix-inner">元</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务链接</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="url" placeholder="请输入任务链接" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">截止时间</label>
                                <div class="el-form-item__content">
                                    <div class="el-date-editor el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="datetime-local" placeholder="请选择截止时间" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="el-dialog__footer">
                    <button type="button" class="el-button" onclick="hideNewTaskDialog()">
                        <span>取消</span>
                    </button>
                    <button type="button" class="el-button el-button--primary">
                        <span>确定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 菜单展开/收起功能
        document.addEventListener('DOMContentLoaded', function() {
            const subMenus = document.querySelectorAll('.el-sub-menu');

            subMenus.forEach(function(subMenu) {
                const title = subMenu.querySelector('.el-sub-menu__title');
                const menuList = subMenu.querySelector('.el-menu--inline');
                const arrow = subMenu.querySelector('.el-sub-menu__icon-arrow');

                if (title && menuList) {
                    title.addEventListener('click', function() {
                        if (subMenu.classList.contains('is-opened')) {
                            subMenu.classList.remove('is-opened');
                            menuList.style.display = 'none';
                        } else {
                            subMenu.classList.add('is-opened');
                            menuList.style.display = 'block';
                        }
                    });
                }
            });
        });

        // 新建任务弹窗功能
        function showNewTaskDialog() {
            document.getElementById('newTaskDialog').style.display = 'block';
            document.body.classList.add('el-popup-parent--hidden');
        }

        function hideNewTaskDialog() {
            document.getElementById('newTaskDialog').style.display = 'none';
            document.body.classList.remove('el-popup-parent--hidden');
        }

        // 点击遮罩层关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
            const dialog = document.getElementById('newTaskDialog');
            if (dialog) {
                dialog.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideNewTaskDialog();
                    }
                });
            }
        });

        // 表单输入框焦点效果
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.el-input__inner');
            inputs.forEach(function(input) {
                input.addEventListener('focus', function() {
                    this.closest('.el-input__wrapper').classList.add('is-focus');
                });

                input.addEventListener('blur', function() {
                    this.closest('.el-input__wrapper').classList.remove('is-focus');
                });
            });
        });
    </script>
</body>
</html>
