<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" class="trancy-zh-CN light">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>北汽声量合伙人管理系统 - 任务管理</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        /* 布局样式 */
        .app-wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar-container {
            width: 210px;
            background: #304156;
            color: white;
            flex-shrink: 0;
        }
        
        .sidebar-logo-container {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #434a50;
        }
        
        .sidebar-logo {
            width: 32px;
            height: 32px;
            vertical-align: middle;
        }
        
        .sidebar-title {
            display: inline-block;
            margin-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            vertical-align: middle;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .navbar {
            height: 50px;
            background: white;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .hamburger-container {
            margin-right: 20px;
            cursor: pointer;
        }
        
        .breadcrumb-container {
            flex: 1;
        }
        
        .right-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .right-menu-item {
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .right-menu-item:hover {
            background-color: #f5f5f5;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        
        .tags-view-container {
            height: 34px;
            background: white;
            border-bottom: 1px solid #d8dce5;
            padding: 0 20px;
            display: flex;
            align-items: center;
        }
        
        .tags-view-item {
            display: inline-block;
            padding: 4px 12px;
            margin-right: 6px;
            background: #fff;
            border: 1px solid #d8dce5;
            color: #495057;
            text-decoration: none;
            font-size: 12px;
            border-radius: 3px;
        }
        
        .tags-view-item.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .app-main {
            flex: 1;
            padding: 20px;
            overflow: auto;
        }
        
        /* 卡片样式 */
        .el-card {
            border-radius: 4px;
            border: 1px solid #ebeef5;
            background-color: #fff;
            overflow: hidden;
            color: #303133;
            transition: .3s;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        
        .el-card__header {
            padding: 18px 20px;
            border-bottom: 1px solid #ebeef5;
            box-sizing: border-box;
        }
        
        .el-card__body {
            padding: 20px;
        }
        
        /* 表单样式 */
        .el-form--inline .el-form-item {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 18px;
            vertical-align: top;
        }
        
        .el-form-item__label {
            text-align: right;
            vertical-align: middle;
            float: left;
            font-size: 14px;
            color: #606266;
            line-height: 40px;
            padding: 0 12px 0 0;
            box-sizing: border-box;
        }
        
        .el-form-item__content {
            line-height: 40px;
            position: relative;
            font-size: 14px;
        }
        
        /* 按钮样式 */
        .el-button {
            display: inline-block;
            line-height: 1;
            white-space: nowrap;
            cursor: pointer;
            background: #fff;
            border: 1px solid #dcdfe6;
            color: #606266;
            text-align: center;
            box-sizing: border-box;
            outline: none;
            margin: 0;
            transition: .1s;
            font-weight: 500;
            padding: 12px 20px;
            font-size: 14px;
            border-radius: 4px;
        }
        
        .el-button--primary {
            color: #fff;
            background-color: #409eff;
            border-color: #409eff;
        }
        
        .el-button--primary:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #fff;
        }
        
        .el-button--warning {
            color: #fff;
            background-color: #e6a23c;
            border-color: #e6a23c;
        }
        
        .el-button.is-plain {
            background: #fff;
            border-color: #409eff;
            color: #409eff;
        }
        
        /* 表格样式 */
        .el-table {
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            flex: 1;
            width: 100%;
            max-width: 100%;
            background-color: #fff;
            font-size: 14px;
            color: #606266;
        }
        
        .el-table__header-wrapper {
            overflow: hidden;
        }
        
        .el-table__header {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 0;
        }
        
        .el-table__cell {
            padding: 12px 0;
            min-width: 0;
            box-sizing: border-box;
            text-overflow: ellipsis;
            vertical-align: middle;
            position: relative;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
        }
        
        .el-table__cell.is-center {
            text-align: center;
        }
        
        .el-table th.el-table__cell {
            background-color: #fafafa;
            color: #909399;
            font-weight: 500;
        }
        
        /* 输入框样式 */
        .el-input {
            position: relative;
            font-size: 14px;
            display: inline-block;
            width: 100%;
        }
        
        .el-input__wrapper {
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: #fff;
            background-image: none;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            box-sizing: border-box;
            transition: border-color .2s cubic-bezier(.645,.045,.355,1);
        }
        
        .el-input__inner {
            width: 100%;
            flex-grow: 1;
            appearance: none;
            color: #606266;
            font-size: inherit;
            height: 32px;
            line-height: 32px;
            padding: 0;
            outline: none;
            border: none;
            background: none;
            box-sizing: border-box;
        }
        
        /* 选择器样式 */
        .el-select {
            display: inline-block;
            position: relative;
        }
        
        .el-select__wrapper {
            cursor: pointer;
            padding: 1px 11px;
            background-color: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            transition: border-color .2s cubic-bezier(.645,.045,.355,1);
        }
        
        /* 菜单样式 */
        .el-menu {
            border: none;
            list-style: none;
            margin: 0;
            padding-left: 0;
            background-color: #304156;
        }
        
        .el-menu-item {
            height: 56px;
            line-height: 56px;
            font-size: 14px;
            color: #bfcbd9;
            padding: 0 20px;
            cursor: pointer;
            position: relative;
            transition: border-color .3s,background-color .3s,color .3s;
            box-sizing: border-box;
            white-space: nowrap;
        }
        
        .el-menu-item:hover {
            background-color: #263445;
        }
        
        .el-menu-item.is-active {
            color: #409eff;
            background-color: #263445;
        }
        
        .el-sub-menu__title {
            height: 56px;
            line-height: 56px;
            padding: 0 20px;
            font-size: 14px;
            color: #bfcbd9;
            position: relative;
            cursor: pointer;
            transition: border-color .3s,background-color .3s,color .3s;
            box-sizing: border-box;
        }
        
        .el-sub-menu.is-active > .el-sub-menu__title {
            color: #409eff;
        }
        
        .el-sub-menu.is-opened > .el-sub-menu__title {
            background-color: #263445;
        }
        
        .menu-title {
            margin-left: 5px;
        }
        
        /* 分页样式 */
        .el-pagination {
            white-space: nowrap;
            padding: 2px 5px;
            color: #303133;
            font-weight: 700;
        }
        
        /* 弹窗样式 */
        .el-overlay {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 2000;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .el-overlay-dialog {
            position: relative;
            margin: 0 auto 50px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            box-sizing: border-box;
            width: 50%;
        }

        .el-dialog {
            position: relative;
            margin: 15vh auto 50px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            box-sizing: border-box;
            width: 50%;
        }

        .el-dialog__header {
            padding: 20px 20px 10px;
        }

        .el-dialog__title {
            line-height: 24px;
            font-size: 18px;
            color: #303133;
        }

        .el-dialog__headerbtn {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 0;
            background: transparent;
            border: none;
            outline: none;
            cursor: pointer;
            font-size: 16px;
        }

        .el-dialog__close {
            color: #909399;
        }

        .el-dialog__body {
            padding: 30px 20px;
            color: #606266;
            font-size: 14px;
            word-break: break-all;
        }

        .el-dialog__footer {
            padding: 10px 20px 20px;
            text-align: right;
            box-sizing: border-box;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 10px;
        }

        .el-textarea {
            position: relative;
            display: inline-block;
            width: 100%;
            vertical-align: bottom;
            font-size: 14px;
        }

        .el-textarea__inner {
            display: block;
            resize: vertical;
            padding: 5px 11px;
            line-height: 1.5;
            box-sizing: border-box;
            width: 100%;
            font-size: inherit;
            color: #606266;
            background-color: #fff;
            background-image: none;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .el-date-editor {
            position: relative;
            display: inline-block;
            text-align: left;
        }

        .el-input__suffix {
            position: absolute;
            height: 100%;
            right: 5px;
            top: 0;
            text-align: center;
            color: #c0c4cc;
            transition: all 0.3s;
            pointer-events: none;
        }

        .el-input__suffix-inner {
            pointer-events: all;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 54px;
            }

            .sidebar-title {
                display: none;
            }

            .el-form--inline .el-form-item {
                display: block;
                margin-right: 0;
            }

            .el-dialog {
                width: 90%;
                margin: 5vh auto 50px;
            }
        }
    </style>
</head>

<body class="el-popup-parent--hidden">
    <div id="app" data-v-app="">
        <div data-v-8bb3bd59="" class="openSidebar app-wrapper">
            <!-- 侧边栏 -->
            <div data-v-8bb3bd59="" class="has-logo sidebar-container">
                <div data-v-c364314f="" class="sidebar-logo-container">
                    <a data-v-c364314f="" href="/manage/" class="sidebar-logo-link">
                        <img data-v-c364314f="" src="/manage/assets/logo-CeJFg1s_.png" class="sidebar-logo" />
                        <h1 data-v-c364314f="" class="sidebar-title">北汽声量合伙人</h1>
                    </a>
                </div>
                <div class="el-scrollbar theme-dark">
                    <div class="scrollbar-wrapper el-scrollbar__wrap el-scrollbar__wrap--hidden-default">
                        <div class="el-scrollbar__view">
                            <ul role="menubar" class="el-menu el-menu--vertical">
                                <div><a href="/manage/index" class=""><li class="el-menu-item submenu-title-noDropdown" role="menuitem" tabindex="-1"><span>🏠</span><span class="menu-title" title="">首页</span></li></a></div>
                                <div><li class="el-sub-menu" role="menuitem"><div class="el-sub-menu__title"><span>👥</span><span class="menu-title" title="">用户管理</span><i class="el-icon el-sub-menu__icon-arrow">▼</i></div></li></div>
                                <div><li class="el-sub-menu" role="menuitem"><div class="el-sub-menu__title"><span>📺</span><span class="menu-title" title="">媒体管理</span><i class="el-icon el-sub-menu__icon-arrow">▼</i></div></li></div>
                                <div><li class="el-sub-menu is-active is-opened" role="menuitem"><div class="el-sub-menu__title"><span>📋</span><span class="menu-title" title="">媒体任务</span><i class="el-icon el-sub-menu__icon-arrow">▼</i></div>
                                    <ul role="menu" class="el-menu el-menu--inline">
                                        <div class="nest-menu"><a href="/manage/mediaTask/mediaTask" class="router-link-active router-link-exact-active"><li class="el-menu-item is-active" role="menuitem" tabindex="-1"><span>📝</span><span class="menu-title" title="">任务管理</span></li></a></div>
                                        <div class="nest-menu"><a href="/manage/mediaTask/taskAudit" class=""><li class="el-menu-item" role="menuitem" tabindex="-1"><span>✅</span><span class="menu-title" title="">任务审核</span></li></a></div>
                                    </ul>
                                </li></div>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div data-v-8bb3bd59="" class="hasTagsView main-container">
                <!-- 导航栏 -->
                <div data-v-8bb3bd59="" class="">
                    <div data-v-48d74461="" data-v-8bb3bd59="" class="navbar">
                        <div data-v-f5d85e4b="" data-v-48d74461="" id="hamburger-container" class="hamburger-container">
                            <span>☰</span>
                        </div>
                        <div data-v-2adfe6a9="" data-v-48d74461="" class="el-breadcrumb app-breadcrumb breadcrumb-container">
                            <span class="el-breadcrumb__item"><span class="el-breadcrumb__inner"><a>首页</a></span><span class="el-breadcrumb__separator">/</span></span>
                            <span class="el-breadcrumb__item"><span class="el-breadcrumb__inner"><span class="no-redirect">媒体任务</span></span><span class="el-breadcrumb__separator">/</span></span>
                            <span class="el-breadcrumb__item"><span class="el-breadcrumb__inner"><span class="no-redirect">任务管理</span></span><span class="el-breadcrumb__separator">/</span></span>
                        </div>
                        <div data-v-48d74461="" class="right-menu flex align-center">
                            <div class="right-menu-item hover-effect">🔍</div>
                            <div class="right-menu-item hover-effect">🔔</div>
                            <div class="right-menu-item hover-effect">⛶</div>
                            <div class="right-menu-item hover-effect">🌐</div>
                            <div class="right-menu-item hover-effect">📏</div>
                            <div class="avatar-container">
                                <div class="right-menu-item hover-effect">
                                    <img src="/manage/assets/profile-ypoSwBLm.jpg" class="user-avatar" />
                                    <i>▼</i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标签页 -->
                    <div data-v-b321c268="" data-v-8bb3bd59="" id="tags-view-container" class="tags-view-container">
                        <div class="scroll-container tags-view-wrapper">
                            <a href="/manage/index" class="tags-view-item">首页</a>
                            <a href="/manage/mediaTask/mediaTask" class="router-link-active router-link-exact-active active tags-view-item">任务管理 <span>×</span></a>
                        </div>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <section data-v-6bbc0a4c="" data-v-8bb3bd59="" class="app-main">
                    <div data-v-b90376a3="" class="p-2">
                        <!-- 搜索表单 -->
                        <div data-v-b90376a3="" class="mb10">
                            <div data-v-b90376a3="" class="el-card is-hover-shadow">
                                <div class="el-card__body">
                                    <form data-v-b90376a3="" class="el-form el-form--default el-form--label-right el-form--inline">
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务ID</label>
                                            <div class="el-form-item__content">
                                                <div class="el-input">
                                                    <div class="el-input__wrapper">
                                                        <input class="el-input__inner" type="text" placeholder="请输入任务ID" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务名称</label>
                                            <div class="el-form-item__content">
                                                <div class="el-input">
                                                    <div class="el-input__wrapper">
                                                        <input class="el-input__inner" type="text" placeholder="请输入任务名称" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <label class="el-form-item__label">任务类型</label>
                                            <div class="el-form-item__content">
                                                <div class="el-select">
                                                    <div class="el-select__wrapper">
                                                        <div class="el-select__selection">
                                                            <div class="el-select__selected-item el-select__placeholder">
                                                                <span>请选择任务类型</span>
                                                            </div>
                                                        </div>
                                                        <div class="el-select__suffix">
                                                            <i class="el-icon el-select__caret">▼</i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="el-form-item">
                                            <div class="el-form-item__content">
                                                <button type="button" class="el-button el-button--primary">
                                                    <i>🔍</i><span>查询</span>
                                                </button>
                                                <button type="button" class="el-button">
                                                    <i>🔄</i><span>重置</span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 数据表格 -->
                        <div class="el-card is-always-shadow">
                            <div class="el-card__header">
                                <div class="el-row">
                                    <div class="el-col">
                                        <button type="button" class="el-button el-button--primary is-plain" onclick="showNewTaskDialog()">
                                            <i>➕</i><span>新建任务</span>
                                        </button>
                                    </div>
                                    <div class="el-col">
                                        <button type="button" class="el-button el-button--warning is-plain">
                                            <i>📤</i><span>导出</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="el-card__body">
                                <div class="el-table">
                                    <div class="el-table__inner-wrapper">
                                        <div class="el-table__header-wrapper">
                                            <table class="el-table__header">
                                                <thead>
                                                    <tr>
                                                        <th class="el-table__cell is-center">ID</th>
                                                        <th class="el-table__cell is-center">任务名称</th>
                                                        <th class="el-table__cell is-center">任务类型</th>
                                                        <th class="el-table__cell is-center">发布账号</th>
                                                        <th class="el-table__cell is-center">任务链接</th>
                                                        <th class="el-table__cell is-center">任务预算(元)</th>
                                                        <th class="el-table__cell is-center">实消预算(元)</th>
                                                        <th class="el-table__cell is-center">领取任务数</th>
                                                        <th class="el-table__cell is-center">提交任务数</th>
                                                        <th class="el-table__cell is-center">完成任务数</th>
                                                        <th class="el-table__cell is-center">PV</th>
                                                        <th class="el-table__cell is-center">UV</th>
                                                        <th class="el-table__cell is-center">有效触达</th>
                                                        <th class="el-table__cell is-center">任务发布时间</th>
                                                        <th class="el-table__cell is-center">任务截止时间</th>
                                                        <th class="el-table__cell is-center">任务状态</th>
                                                        <th class="el-table__cell is-center">操作</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                        <div class="el-table__body-wrapper">
                                            <table class="el-table__body">
                                                <tbody>
                                                    <tr class="el-table__row">
                                                        <td class="el-table__cell is-center">1950130139070304258</td>
                                                        <td class="el-table__cell is-center">0729微信任务-2 考核</td>
                                                        <td class="el-table__cell is-center">转评赞</td>
                                                        <td class="el-table__cell is-center">admin</td>
                                                        <td class="el-table__cell is-center">https://mp.weixin.qq.com/s/...</td>
                                                        <td class="el-table__cell is-center">1000.00</td>
                                                        <td class="el-table__cell is-center">0.00</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">0</td>
                                                        <td class="el-table__cell is-center">2024-07-29 17:30:00</td>
                                                        <td class="el-table__cell is-center">2024-08-05 23:59:59</td>
                                                        <td class="el-table__cell is-center">已上架</td>
                                                        <td class="el-table__cell is-center">
                                                            <button class="el-button el-button--primary">编辑</button>
                                                            <button class="el-button el-button--danger">删除</button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 新建任务弹窗 -->
    <div id="newTaskDialog" class="el-overlay" style="display: none; z-index: 2000;">
        <div class="el-overlay-dialog">
            <div class="el-dialog" style="margin-top: 15vh; width: 50%;">
                <div class="el-dialog__header">
                    <span class="el-dialog__title">新建任务</span>
                    <button class="el-dialog__headerbtn" onclick="hideNewTaskDialog()">
                        <i class="el-dialog__close">×</i>
                    </button>
                </div>
                <div class="el-dialog__body">
                    <form class="el-form el-form--default el-form--label-right">
                        <!-- 基础信息 -->
                        <div class="form-section">
                            <h3>基础信息</h3>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务名称</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="text" placeholder="请输入任务名称" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务类型</label>
                                <div class="el-form-item__content">
                                    <div class="el-select">
                                        <div class="el-select__wrapper">
                                            <div class="el-select__selection">
                                                <div class="el-select__selected-item el-select__placeholder">
                                                    <span>请选择任务类型</span>
                                                </div>
                                            </div>
                                            <div class="el-select__suffix">
                                                <i class="el-icon el-select__caret">▼</i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务描述</label>
                                <div class="el-form-item__content">
                                    <div class="el-textarea">
                                        <textarea class="el-textarea__inner" placeholder="请输入任务描述" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 任务设置 -->
                        <div class="form-section">
                            <h3>任务设置</h3>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务预算</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="number" placeholder="请输入任务预算" />
                                        </div>
                                        <div class="el-input__suffix">
                                            <span class="el-input__suffix-inner">元</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">任务链接</label>
                                <div class="el-form-item__content">
                                    <div class="el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="url" placeholder="请输入任务链接" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="el-form-item">
                                <label class="el-form-item__label">截止时间</label>
                                <div class="el-form-item__content">
                                    <div class="el-date-editor el-input">
                                        <div class="el-input__wrapper">
                                            <input class="el-input__inner" type="datetime-local" placeholder="请选择截止时间" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="el-dialog__footer">
                    <button type="button" class="el-button" onclick="hideNewTaskDialog()">
                        <span>取消</span>
                    </button>
                    <button type="button" class="el-button el-button--primary">
                        <span>确定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showNewTaskDialog() {
            document.getElementById('newTaskDialog').style.display = 'block';
            document.body.classList.add('el-popup-parent--hidden');
        }

        function hideNewTaskDialog() {
            document.getElementById('newTaskDialog').style.display = 'none';
            document.body.classList.remove('el-popup-parent--hidden');
        }

        // 点击遮罩层关闭弹窗
        document.getElementById('newTaskDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                hideNewTaskDialog();
            }
        });
    </script>
</body>
</html>
